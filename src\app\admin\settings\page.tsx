"use client";

import React, { useState } from "react";
import { Settings, Save, User, Phone } from "lucide-react";
import { PageHeader } from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { AdminManagement } from "@/components/admin/AdminManagement";
import { toast } from "sonner";

export default function SettingsPage() {
  const [adminPhone, setAdminPhone] = useState(
    process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER || ""
  );
  const [saving, setSaving] = useState(false);

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      // TODO: Implement save settings to Firestore
      toast.success("Settings saved successfully");
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Settings"
        description="Configure your education management system"
      />

      <AdminManagement />

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Legacy Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="adminPhone">Environment Admin Phone</Label>
              <Input
                id="adminPhone"
                type="tel"
                placeholder="+1234567890"
                value={adminPhone}
                onChange={(e) => setAdminPhone(e.target.value)}
                disabled
              />
              <p className="text-sm text-muted-foreground">
                This is the phone number from environment variables (read-only).
                Use Admin Management above to add/remove admin access.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>System Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Version:</span>
                <span className="text-sm text-muted-foreground">1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Environment:</span>
                <span className="text-sm text-muted-foreground">
                  Development
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Database:</span>
                <span className="text-sm text-muted-foreground">
                  Firebase Firestore
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Field Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Course Fields</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Configure which fields are required when creating courses
              </p>
              <Button
                variant="outline"
                onClick={() => toast.info("Field configuration coming soon")}
              >
                Configure Course Fields
              </Button>
            </div>

            <Separator />

            <div>
              <h4 className="text-sm font-medium mb-2">Student Fields</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Configure which fields are required when adding students
              </p>
              <Button
                variant="outline"
                onClick={() => toast.info("Field configuration coming soon")}
              >
                Configure Student Fields
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Data Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2 md:grid-cols-3">
            <Button
              variant="outline"
              onClick={() => toast.info("Feature coming soon")}
            >
              Export Data
            </Button>
            <Button
              variant="outline"
              onClick={() => toast.info("Feature coming soon")}
            >
              Import Data
            </Button>
            <Button
              variant="outline"
              onClick={() => toast.info("Feature coming soon")}
            >
              Backup Database
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Manage your data with export, import, and backup functionality.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
