import { Timestamp } from "firebase/firestore";

// Field Configuration Types
export interface FieldDefinition {
  id: string;
  label: string;
  type: "text" | "email" | "phone" | "number" | "textarea" | "select" | "date";
  required: boolean;
  options?: string[]; // For select fields
  placeholder?: string;
}

// Admin Settings
export interface AdminSettings {
  courseFields: FieldDefinition[];
  studentFields: FieldDefinition[];
  adminPhoneNumber: string;
  adminEmails: string[];
  adminPhoneNumbers: string[];
}

// Admin User Management
export interface AdminUser {
  id: string;
  email?: string;
  phoneNumber?: string;
  displayName?: string;
  createdAt: Timestamp;
  createdBy: string;
  isActive: boolean;
}

// Login Types
export type LoginMethod = "phone" | "email";

export interface LoginFormData {
  identifier: string; // phone number or email
  method: LoginMethod;
}

// Course Types
export interface Course {
  id: string;
  name: string;
  description?: string;
  fee: number;
  schedule?: string;
  duration?: string;
  startDate?: Timestamp;
  endDate?: Timestamp;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  customFields: Record<string, any>;
}

export interface CourseFormData {
  name: string;
  description?: string;
  fee: number;
  schedule?: string;
  duration?: string;
  startDate?: Date;
  endDate?: Date;
  customFields: Record<string, any>;
}

// Student Types
export interface Student {
  id: string;
  courseId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  parentContact?: string;
  enrollmentDate: Timestamp;
  isActive: boolean;
  customFields: Record<string, any>;
}

export interface StudentFormData {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  parentContact?: string;
  customFields: Record<string, any>;
}

// Attendance Types
export interface AttendanceRecord {
  studentId: string;
  present: boolean;
  timestamp: Timestamp;
  notes?: string;
}

export interface AttendanceSheet {
  courseId: string;
  date: string; // YYYY-MM-DD format
  records: Record<string, AttendanceRecord>;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Exam Types
export interface Exam {
  id: string;
  courseId: string;
  name: string;
  description?: string;
  date: Timestamp;
  totalMarks: number;
  passingMarks?: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ExamResult {
  studentId: string;
  examId: string;
  marks: number;
  grade?: string;
  percentage: number;
  passed: boolean;
  submittedAt: Timestamp;
}

// Payment Types
export type PaymentStatus = "due" | "paid" | "overdue";

export interface Payment {
  studentId: string;
  courseId: string;
  monthYear: string; // Format: "January2025"
  amount: number;
  discount: number;
  finalAmount: number;
  status: PaymentStatus;
  dueDate: Timestamp;
  paidDate?: Timestamp;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Analytics Types
export interface StudentAnalytics {
  studentId: string;
  courseId: string;
  attendancePercentage: number;
  totalClasses: number;
  attendedClasses: number;
  averageGrade: number;
  totalExams: number;
  passedExams: number;
  paymentStatus: {
    totalDue: number;
    totalPaid: number;
    overdueMonths: number;
  };
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
}

// Search and Filter Types
export interface SearchFilters {
  query?: string;
  courseId?: string;
  status?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Navigation Types
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  children?: NavItem[];
}

// Dashboard Stats
export interface DashboardStats {
  totalCourses: number;
  totalStudents: number;
  totalRevenue: number;
  pendingPayments: number;
  averageAttendance: number;
  activeExams: number;
}
