'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, BookOpen } from 'lucide-react';
import { PageHeader } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StudentForm } from '@/components/students/StudentForm';
import { Student, StudentFormData, Course } from '@/types';
import { createStudent, getStudents, updateStudent, deleteStudent, getCourses } from '@/lib/firestore';
import { toast } from 'sonner';
import { format } from 'date-fns';

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [studentsData, coursesData] = await Promise.all([
        getStudents(),
        getCourses(),
      ]);
      setStudents(studentsData);
      setCourses(coursesData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStudent = async (data: StudentFormData & { courseId: string }) => {
    try {
      await createStudent(data.courseId, data);
      toast.success('Student added successfully');
      loadData();
    } catch (error) {
      console.error('Error creating student:', error);
      toast.error('Failed to add student');
    }
  };

  const handleUpdateStudent = async (data: StudentFormData & { courseId: string }) => {
    if (!editingStudent) return;
    
    try {
      await updateStudent(editingStudent.id, data);
      toast.success('Student updated successfully');
      setEditingStudent(null);
      loadData();
    } catch (error) {
      console.error('Error updating student:', error);
      toast.error('Failed to update student');
    }
  };

  const handleDeleteStudent = async (studentId: string) => {
    if (!confirm('Are you sure you want to delete this student?')) return;
    
    try {
      await deleteStudent(studentId);
      toast.success('Student deleted successfully');
      loadData();
    } catch (error) {
      console.error('Error deleting student:', error);
      toast.error('Failed to delete student');
    }
  };

  const getCourseName = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    return course?.name || 'Unknown Course';
  };

  const columns: Column<Student>[] = [
    {
      key: 'name',
      header: 'Student Name',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.email && (
            <div className="text-sm text-muted-foreground">{row.email}</div>
          )}
        </div>
      ),
    },
    {
      key: 'courseId',
      header: 'Course',
      render: (value) => (
        <div className="flex items-center space-x-2">
          <BookOpen className="h-4 w-4 text-muted-foreground" />
          <span>{getCourseName(value)}</span>
        </div>
      ),
    },
    {
      key: 'phone',
      header: 'Phone',
      render: (value) => value || 'Not provided',
    },
    {
      key: 'parentContact',
      header: 'Parent Contact',
      render: (value) => value || 'Not provided',
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'enrollmentDate',
      header: 'Enrolled',
      render: (value) => format(value.toDate(), 'MMM dd, yyyy'),
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Students"
        description="Manage students across all courses"
        action={{
          label: 'Add Student',
          onClick: () => setShowForm(true),
        }}
      />

      <DataTable
        data={students}
        columns={columns}
        loading={loading}
        searchable
        searchPlaceholder="Search students..."
        emptyMessage="No students found. Add your first student to get started."
        actions={(student) => (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setEditingStudent(student);
                setShowForm(true);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteStudent(student.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}
      />

      <StudentForm
        open={showForm}
        onOpenChange={(open) => {
          setShowForm(open);
          if (!open) {
            setEditingStudent(null);
          }
        }}
        onSubmit={editingStudent ? handleUpdateStudent : handleCreateStudent}
        initialData={editingStudent ? {
          name: editingStudent.name,
          email: editingStudent.email,
          phone: editingStudent.phone,
          address: editingStudent.address,
          parentContact: editingStudent.parentContact,
          customFields: editingStudent.customFields,
        } : undefined}
        title={editingStudent ? 'Edit Student' : 'Add Student'}
        description={editingStudent ? 'Update student information' : 'Add a new student to a course'}
      />
    </div>
  );
}
