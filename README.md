# Education Management System

A comprehensive student management web application designed for teachers to manage their offline classes. Built with Next.js, TypeScript, Firebase, and shadcn/ui.

## Features

### 🔐 Authentication & Security

- **Dual Authentication Methods**: Support for both phone number (OTP) and email/password authentication
- **Multi-Admin Support**: <PERSON>mins can add/remove other admin accounts via email or phone
- **Flexible Admin Management**: Dynamic admin list stored in Firestore, not just environment variables
- **Secure Session Management**: Firebase-based authentication with proper session handling
- **Admin-Only Access Control**: Only registered admin accounts can access the system

### 📚 Course Management

- Create and manage courses with customizable fields
- Configure required information for course creation
- Track course schedules, fees, and duration
- Active/inactive course status management

### 👥 Student Management

- Enroll students in courses with configurable fields
- Comprehensive student profiles with custom information
- Search and filter students across all courses
- Student enrollment tracking and management

### 📅 Attendance System

- Daily attendance sheets for each course
- Interactive attendance marking with checkboxes
- Search functionality within attendance sheets
- Attendance history and analytics
- Quick actions (mark all present/absent)

### 📝 Examination & Grading

- Create exams for specific courses
- Grade entry and management system
- Performance tracking and analytics
- Exam scheduling and organization

### 💰 Payment Management

- Monthly payment structure with automated billing
- Payment status tracking (due, paid, overdue)
- Discount management for individual students
- Financial analytics and reporting
- Flexible payment timeline management

### 📊 Analytics & Reporting

- Student performance analytics
- Attendance percentage calculations
- Payment status overview
- Dashboard with key metrics and statistics

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **UI Components**: shadcn/ui, Tailwind CSS
- **Backend**: Firebase (Authentication, Firestore)
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Notifications**: Sonner

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd eds
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:
   Create a `.env.local` file in the root directory:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Admin Configuration
NEXT_PUBLIC_ADMIN_PHONE_NUMBER=+**********
```

4. Set up Firebase:

   - Create a new Firebase project
   - Enable Authentication with Phone Number provider
   - Create a Firestore database
   - Add your domain to authorized domains

5. Run the development server:

```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Firebase Setup

### Authentication

1. Go to Firebase Console → Authentication → Sign-in method
2. Enable "Phone" provider for OTP authentication
3. Enable "Email/Password" provider for email authentication
4. Add your domain to authorized domains
5. For email authentication, you'll need to create user accounts manually in Firebase Console or use the admin panel to add admin emails

### Firestore Database

Create the following collections structure:

```
courses/
  {courseID}/
    courseInfo: {name, description, fee, etc.}

students/
  {studentID}: {studentData, courseId, etc.}

attendance/
  {courseId_date}/
    records: {studentId: {present: boolean, timestamp}}

exams/
  {examID}/
    examInfo: {name, date, totalMarks}

payments/
  {studentId_monthYear}: {amount, status, etc.}

adminSettings/
  config: {courseFields, studentFields, adminPhoneNumber, adminEmails, adminPhoneNumbers}

adminUsers/
  {userId}: {email/phoneNumber, createdAt, createdBy, isActive}
```

## Usage

### Admin Login

#### Phone Number Authentication

1. Visit the application URL
2. Select the "Phone" tab
3. Enter your admin phone number
4. Receive and enter the OTP
5. Access the admin dashboard

#### Email Authentication

1. Visit the application URL
2. Select the "Email" tab
3. Enter your admin email and password
4. Click "Sign In" to access the admin dashboard
5. Use "Forgot password?" if you need to reset your password

### Admin Management

#### Adding New Admins

1. Go to Settings in the admin panel
2. In the "Admin Management" section, click "Add Admin"
3. Choose either Email or Phone tab
4. Enter the new admin's email address or phone number
5. Click "Add Admin" to grant access

#### Removing Admin Access

1. Go to Settings → Admin Management
2. Find the admin you want to remove
3. Click the trash icon next to their email/phone
4. Confirm the removal (you cannot remove your own access)

### Managing Courses

1. Navigate to "Courses" in the sidebar
2. Click "Add Course" to create a new course
3. Fill in course details (name, fee, schedule, etc.)
4. Manage existing courses with edit/delete options

### Managing Students

1. Go to "Students" section
2. Add students to specific courses
3. Configure student information fields
4. Search and filter students as needed

### Taking Attendance

1. Visit "Attendance" section
2. Select a course and date
3. Mark students as present/absent
4. Use quick actions for bulk operations
5. Save attendance records

### Payment Tracking

1. Access "Payments" section
2. View payment status across all students
3. Mark payments as received
4. Generate monthly bills
5. Track overdue payments

## Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── admin/             # Admin panel pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home/login page
├── components/            # React components
│   ├── auth/              # Authentication components
│   ├── courses/           # Course management components
│   ├── layout/            # Layout components
│   ├── students/          # Student management components
│   └── ui/                # shadcn/ui components
├── contexts/              # React contexts
├── lib/                   # Utility functions and configurations
├── types/                 # TypeScript type definitions
└── hooks/                 # Custom React hooks
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
