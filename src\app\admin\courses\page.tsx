'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Users, Calendar } from 'lucide-react';
import { PageHeader } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CourseForm } from '@/components/courses/CourseForm';
import { Course, CourseFormData } from '@/types';
import { createCourse, getCourses, updateCourse, deleteCourse } from '@/lib/firestore';
import { toast } from 'sonner';
import { format } from 'date-fns';

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      setLoading(true);
      const coursesData = await getCourses();
      setCourses(coursesData);
    } catch (error) {
      console.error('Error loading courses:', error);
      toast.error('Failed to load courses');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCourse = async (data: CourseFormData) => {
    try {
      await createCourse(data);
      toast.success('Course created successfully');
      loadCourses();
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error('Failed to create course');
    }
  };

  const handleUpdateCourse = async (data: CourseFormData) => {
    if (!editingCourse) return;
    
    try {
      await updateCourse(editingCourse.id, data);
      toast.success('Course updated successfully');
      setEditingCourse(null);
      loadCourses();
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error('Failed to update course');
    }
  };

  const handleDeleteCourse = async (courseId: string) => {
    if (!confirm('Are you sure you want to delete this course?')) return;
    
    try {
      await deleteCourse(courseId);
      toast.success('Course deleted successfully');
      loadCourses();
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('Failed to delete course');
    }
  };

  const columns: Column<Course>[] = [
    {
      key: 'name',
      header: 'Course Name',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.description && (
            <div className="text-sm text-muted-foreground">{row.description}</div>
          )}
        </div>
      ),
    },
    {
      key: 'fee',
      header: 'Fee',
      render: (value) => `₹${value.toLocaleString()}`,
    },
    {
      key: 'schedule',
      header: 'Schedule',
      render: (value) => value || 'Not specified',
    },
    {
      key: 'duration',
      header: 'Duration',
      render: (value) => value || 'Not specified',
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (value) => format(value.toDate(), 'MMM dd, yyyy'),
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Courses"
        description="Manage your courses and their configurations"
        action={{
          label: 'Add Course',
          onClick: () => setShowForm(true),
        }}
      />

      <DataTable
        data={courses}
        columns={columns}
        loading={loading}
        searchable
        searchPlaceholder="Search courses..."
        emptyMessage="No courses found. Create your first course to get started."
        actions={(course) => (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setEditingCourse(course);
                setShowForm(true);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteCourse(course.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}
      />

      <CourseForm
        open={showForm}
        onOpenChange={(open) => {
          setShowForm(open);
          if (!open) {
            setEditingCourse(null);
          }
        }}
        onSubmit={editingCourse ? handleUpdateCourse : handleCreateCourse}
        initialData={editingCourse ? {
          name: editingCourse.name,
          description: editingCourse.description,
          fee: editingCourse.fee,
          schedule: editingCourse.schedule,
          duration: editingCourse.duration,
          startDate: editingCourse.startDate?.toDate(),
          endDate: editingCourse.endDate?.toDate(),
          customFields: editingCourse.customFields,
        } : undefined}
        title={editingCourse ? 'Edit Course' : 'Create Course'}
        description={editingCourse ? 'Update course information' : 'Add a new course to your system'}
      />
    </div>
  );
}
