'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, FileText, Users } from 'lucide-react';
import { PageHeader } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Exam, Course } from '@/types';
import { getCourses } from '@/lib/firestore';
import { toast } from 'sonner';
import { format } from 'date-fns';

export default function ExamsPage() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const coursesData = await getCourses();
      setCourses(coursesData);
      
      // TODO: Load exams from Firestore
      setExams([]);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const getCourseName = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    return course?.name || 'Unknown Course';
  };

  const columns: Column<Exam>[] = [
    {
      key: 'name',
      header: 'Exam Name',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.description && (
            <div className="text-sm text-muted-foreground">{row.description}</div>
          )}
        </div>
      ),
    },
    {
      key: 'courseId',
      header: 'Course',
      render: (value) => getCourseName(value),
    },
    {
      key: 'date',
      header: 'Exam Date',
      render: (value) => format(value.toDate(), 'MMM dd, yyyy'),
    },
    {
      key: 'totalMarks',
      header: 'Total Marks',
      render: (value) => value,
    },
    {
      key: 'passingMarks',
      header: 'Passing Marks',
      render: (value) => value || 'Not set',
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Exams"
        description="Manage exams and track student performance"
        action={{
          label: 'Create Exam',
          onClick: () => toast.info('Exam creation form coming soon'),
        }}
      />

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{exams.length}</div>
            <p className="text-xs text-muted-foreground">
              Across all courses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Exams</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Next 7 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Results</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Results to be entered
            </p>
          </CardContent>
        </Card>
      </div>

      <DataTable
        data={exams}
        columns={columns}
        loading={loading}
        searchable
        searchPlaceholder="Search exams..."
        emptyMessage="No exams found. Create your first exam to get started."
        actions={(exam) => (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toast.info('Grade entry coming soon')}
            >
              <Users className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toast.info('Edit exam coming soon')}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toast.info('Delete exam coming soon')}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}
      />

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2 md:grid-cols-2">
            <Button variant="outline" onClick={() => toast.info('Feature coming soon')}>
              <FileText className="h-4 w-4 mr-2" />
              Create Exam
            </Button>
            <Button variant="outline" onClick={() => toast.info('Feature coming soon')}>
              <Users className="h-4 w-4 mr-2" />
              Enter Grades
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Exam management features are being developed. You can create exams, enter grades, and track student performance.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
