import {
  signInWithPhoneNumber,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  RecaptchaVerifier,
  ConfirmationResult,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User,
} from "firebase/auth";
import { auth, db } from "./firebase";
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
} from "firebase/firestore";
import type { AdminSettings, AdminUser } from "@/types";

// Initialize reCAPTCHA verifier
export const initializeRecaptcha = (elementId: string): RecaptchaVerifier => {
  return new RecaptchaVerifier(auth, elementId, {
    size: "invisible",
    callback: () => {
      // reCAPTCHA solved
    },
    "expired-callback": () => {
      // Response expired
    },
  });
};

// Send OTP to phone number
export const sendOTP = async (
  phoneNumber: string,
  recaptchaVerifier: RecaptchaVerifier
): Promise<ConfirmationResult> => {
  try {
    const confirmationResult = await signInWithPhoneNumber(
      auth,
      phoneNumber,
      recaptchaVerifier
    );
    return confirmationResult;
  } catch (error) {
    console.error("Error sending OTP:", error);
    throw error;
  }
};

// Verify OTP and sign in
export const verifyOTP = async (
  confirmationResult: ConfirmationResult,
  otp: string
): Promise<User> => {
  try {
    const result = await confirmationResult.confirm(otp);
    return result.user;
  } catch (error) {
    console.error("Error verifying OTP:", error);
    throw error;
  }
};

// Email authentication
export const signInWithEmail = async (
  email: string,
  password: string
): Promise<User> => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password);
    return result.user;
  } catch (error) {
    console.error("Error signing in with email:", error);
    throw error;
  }
};

// Send password reset email
export const sendPasswordReset = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error("Error sending password reset email:", error);
    throw error;
  }
};

// Get admin settings from Firestore
export const getAdminSettings = async (): Promise<AdminSettings | null> => {
  try {
    const settingsRef = doc(db, "adminSettings", "config");
    const settingsSnap = await getDoc(settingsRef);

    if (settingsSnap.exists()) {
      return settingsSnap.data() as AdminSettings;
    }

    // Return default settings if none exist
    return {
      courseFields: [],
      studentFields: [],
      adminPhoneNumber: process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER || "",
      adminEmails: [],
      adminPhoneNumbers: [process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER || ""],
    };
  } catch (error) {
    console.error("Error getting admin settings:", error);
    return null;
  }
};

// Check if user is admin (enhanced to check both phone and email)
export const isAdmin = async (user: User | null): Promise<boolean> => {
  if (!user) return false;

  try {
    const settings = await getAdminSettings();
    if (!settings) return false;

    // Check phone number
    if (
      user.phoneNumber &&
      settings.adminPhoneNumbers.includes(user.phoneNumber)
    ) {
      return true;
    }

    // Check email
    if (user.email && settings.adminEmails.includes(user.email)) {
      return true;
    }

    // Fallback to environment variable for backward compatibility
    const envAdminPhone = process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER;
    if (user.phoneNumber === envAdminPhone) {
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error checking admin status:", error);
    // Fallback to environment variable check
    const envAdminPhone = process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER;
    return user.phoneNumber === envAdminPhone;
  }
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error("Error signing out:", error);
    throw error;
  }
};

// Auth state observer
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

// Admin management functions
export const addAdminEmail = async (
  email: string,
  currentUserId: string
): Promise<void> => {
  try {
    const settingsRef = doc(db, "adminSettings", "config");
    await updateDoc(settingsRef, {
      adminEmails: arrayUnion(email),
    });

    // Log the admin addition
    const adminUserRef = doc(
      db,
      "adminUsers",
      email.replace("@", "_at_").replace(".", "_dot_")
    );
    await setDoc(adminUserRef, {
      email,
      createdAt: new Date(),
      createdBy: currentUserId,
      isActive: true,
      type: "email",
    });
  } catch (error) {
    console.error("Error adding admin email:", error);
    throw error;
  }
};

export const addAdminPhone = async (
  phoneNumber: string,
  currentUserId: string
): Promise<void> => {
  try {
    const settingsRef = doc(db, "adminSettings", "config");
    await updateDoc(settingsRef, {
      adminPhoneNumbers: arrayUnion(phoneNumber),
    });

    // Log the admin addition
    const adminUserRef = doc(
      db,
      "adminUsers",
      phoneNumber.replace("+", "plus_").replace(" ", "_")
    );
    await setDoc(adminUserRef, {
      phoneNumber,
      createdAt: new Date(),
      createdBy: currentUserId,
      isActive: true,
      type: "phone",
    });
  } catch (error) {
    console.error("Error adding admin phone:", error);
    throw error;
  }
};

export const removeAdminEmail = async (email: string): Promise<void> => {
  try {
    const settingsRef = doc(db, "adminSettings", "config");
    await updateDoc(settingsRef, {
      adminEmails: arrayRemove(email),
    });

    // Deactivate the admin user record
    const adminUserRef = doc(
      db,
      "adminUsers",
      email.replace("@", "_at_").replace(".", "_dot_")
    );
    await updateDoc(adminUserRef, {
      isActive: false,
      deactivatedAt: new Date(),
    });
  } catch (error) {
    console.error("Error removing admin email:", error);
    throw error;
  }
};

export const removeAdminPhone = async (phoneNumber: string): Promise<void> => {
  try {
    const settingsRef = doc(db, "adminSettings", "config");
    await updateDoc(settingsRef, {
      adminPhoneNumbers: arrayRemove(phoneNumber),
    });

    // Deactivate the admin user record
    const adminUserRef = doc(
      db,
      "adminUsers",
      phoneNumber.replace("+", "plus_").replace(" ", "_")
    );
    await updateDoc(adminUserRef, {
      isActive: false,
      deactivatedAt: new Date(),
    });
  } catch (error) {
    console.error("Error removing admin phone:", error);
    throw error;
  }
};

// Initialize admin settings if they don't exist
export const initializeAdminSettings = async (): Promise<void> => {
  try {
    const settingsRef = doc(db, "adminSettings", "config");
    const settingsSnap = await getDoc(settingsRef);

    if (!settingsSnap.exists()) {
      const defaultSettings: AdminSettings = {
        courseFields: [],
        studentFields: [],
        adminPhoneNumber: process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER || "",
        adminEmails: [],
        adminPhoneNumbers: [process.env.NEXT_PUBLIC_ADMIN_PHONE_NUMBER || ""],
      };

      await setDoc(settingsRef, defaultSettings);
    }
  } catch (error) {
    console.error("Error initializing admin settings:", error);
    throw error;
  }
};
