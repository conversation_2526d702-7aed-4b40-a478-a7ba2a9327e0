import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from './firebase';
import type {
  Course,
  CourseFormData,
  Student,
  StudentFormData,
  AttendanceSheet,
  AttendanceRecord,
  Exam,
  ExamResult,
  Payment,
  AdminSettings,
} from '@/types';

// Collections
const COLLECTIONS = {
  COURSES: 'courses',
  STUDENTS: 'students',
  ATTENDANCE: 'attendance',
  EXAMS: 'exams',
  EXAM_RESULTS: 'examResults',
  PAYMENTS: 'payments',
  ADMIN_SETTINGS: 'adminSettings',
} as const;

// Course operations
export const createCourse = async (courseData: CourseFormData): Promise<string> => {
  const courseRef = await addDoc(collection(db, COLLECTIONS.COURSES), {
    ...courseData,
    startDate: courseData.startDate ? Timestamp.fromDate(courseData.startDate) : null,
    endDate: courseData.endDate ? Timestamp.fromDate(courseData.endDate) : null,
    isActive: true,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  });
  return courseRef.id;
};

export const getCourses = async (): Promise<Course[]> => {
  const coursesRef = collection(db, COLLECTIONS.COURSES);
  const q = query(coursesRef, orderBy('createdAt', 'desc'));
  const snapshot = await getDocs(q);
  
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  })) as Course[];
};

export const getCourse = async (courseId: string): Promise<Course | null> => {
  const courseRef = doc(db, COLLECTIONS.COURSES, courseId);
  const courseSnap = await getDoc(courseRef);
  
  if (courseSnap.exists()) {
    return {
      id: courseSnap.id,
      ...courseSnap.data(),
    } as Course;
  }
  
  return null;
};

export const updateCourse = async (courseId: string, courseData: Partial<CourseFormData>): Promise<void> => {
  const courseRef = doc(db, COLLECTIONS.COURSES, courseId);
  const updateData: any = {
    ...courseData,
    updatedAt: Timestamp.now(),
  };
  
  if (courseData.startDate) {
    updateData.startDate = Timestamp.fromDate(courseData.startDate);
  }
  if (courseData.endDate) {
    updateData.endDate = Timestamp.fromDate(courseData.endDate);
  }
  
  await updateDoc(courseRef, updateData);
};

export const deleteCourse = async (courseId: string): Promise<void> => {
  const courseRef = doc(db, COLLECTIONS.COURSES, courseId);
  await deleteDoc(courseRef);
};

// Student operations
export const createStudent = async (courseId: string, studentData: StudentFormData): Promise<string> => {
  const studentRef = await addDoc(collection(db, COLLECTIONS.STUDENTS), {
    ...studentData,
    courseId,
    enrollmentDate: Timestamp.now(),
    isActive: true,
  });
  return studentRef.id;
};

export const getStudents = async (courseId?: string): Promise<Student[]> => {
  const studentsRef = collection(db, COLLECTIONS.STUDENTS);
  let q = query(studentsRef, orderBy('enrollmentDate', 'desc'));
  
  if (courseId) {
    q = query(studentsRef, where('courseId', '==', courseId), orderBy('enrollmentDate', 'desc'));
  }
  
  const snapshot = await getDocs(q);
  
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  })) as Student[];
};

export const getStudent = async (studentId: string): Promise<Student | null> => {
  const studentRef = doc(db, COLLECTIONS.STUDENTS, studentId);
  const studentSnap = await getDoc(studentRef);
  
  if (studentSnap.exists()) {
    return {
      id: studentSnap.id,
      ...studentSnap.data(),
    } as Student;
  }
  
  return null;
};

export const updateStudent = async (studentId: string, studentData: Partial<StudentFormData>): Promise<void> => {
  const studentRef = doc(db, COLLECTIONS.STUDENTS, studentId);
  await updateDoc(studentRef, studentData);
};

export const deleteStudent = async (studentId: string): Promise<void> => {
  const studentRef = doc(db, COLLECTIONS.STUDENTS, studentId);
  await deleteDoc(studentRef);
};

// Admin Settings operations
export const getAdminSettings = async (): Promise<AdminSettings | null> => {
  const settingsRef = doc(db, COLLECTIONS.ADMIN_SETTINGS, 'config');
  const settingsSnap = await getDoc(settingsRef);
  
  if (settingsSnap.exists()) {
    return settingsSnap.data() as AdminSettings;
  }
  
  return null;
};

export const updateAdminSettings = async (settings: AdminSettings): Promise<void> => {
  const settingsRef = doc(db, COLLECTIONS.ADMIN_SETTINGS, 'config');
  await updateDoc(settingsRef, settings);
};
