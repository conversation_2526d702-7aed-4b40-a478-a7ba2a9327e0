'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, Users, Save, BookOpen } from 'lucide-react';
import { PageHeader } from '@/components/ui/page-header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Student, Course } from '@/types';
import { getStudents, getCourses } from '@/lib/firestore';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface AttendanceRecord {
  studentId: string;
  present: boolean;
}

export default function AttendancePage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState('');
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [attendance, setAttendance] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadCourses();
  }, []);

  useEffect(() => {
    if (selectedCourseId) {
      loadStudents();
    }
  }, [selectedCourseId]);

  const loadCourses = async () => {
    try {
      const coursesData = await getCourses();
      setCourses(coursesData.filter(course => course.isActive));
    } catch (error) {
      console.error('Error loading courses:', error);
      toast.error('Failed to load courses');
    }
  };

  const loadStudents = async () => {
    try {
      setLoading(true);
      const studentsData = await getStudents(selectedCourseId);
      setStudents(studentsData.filter(student => student.isActive));
      
      // Initialize attendance state
      const initialAttendance: Record<string, boolean> = {};
      studentsData.forEach(student => {
        initialAttendance[student.id] = false;
      });
      setAttendance(initialAttendance);
    } catch (error) {
      console.error('Error loading students:', error);
      toast.error('Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  const handleAttendanceChange = (studentId: string, present: boolean) => {
    setAttendance(prev => ({
      ...prev,
      [studentId]: present,
    }));
  };

  const handleSaveAttendance = async () => {
    if (!selectedCourseId || !selectedDate) {
      toast.error('Please select a course and date');
      return;
    }

    try {
      setSaving(true);
      // TODO: Implement save attendance to Firestore
      console.log('Saving attendance:', {
        courseId: selectedCourseId,
        date: selectedDate,
        attendance,
      });
      toast.success('Attendance saved successfully');
    } catch (error) {
      console.error('Error saving attendance:', error);
      toast.error('Failed to save attendance');
    } finally {
      setSaving(false);
    }
  };

  const handleMarkAll = (present: boolean) => {
    const newAttendance: Record<string, boolean> = {};
    filteredStudents.forEach(student => {
      newAttendance[student.id] = present;
    });
    setAttendance(prev => ({ ...prev, ...newAttendance }));
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const presentCount = filteredStudents.filter(student => attendance[student.id]).length;
  const totalCount = filteredStudents.length;

  return (
    <div className="space-y-6">
      <PageHeader
        title="Attendance"
        description="Mark daily attendance for your courses"
      />

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5" />
              <span>Course Selection</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Select Course</Label>
              <Select value={selectedCourseId} onValueChange={setSelectedCourseId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Date</Label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Attendance Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Present:</span>
                <span className="font-semibold text-green-600">{presentCount}</span>
              </div>
              <div className="flex justify-between">
                <span>Absent:</span>
                <span className="font-semibold text-red-600">{totalCount - presentCount}</span>
              </div>
              <div className="flex justify-between">
                <span>Total:</span>
                <span className="font-semibold">{totalCount}</span>
              </div>
              <div className="flex justify-between">
                <span>Attendance Rate:</span>
                <span className="font-semibold">
                  {totalCount > 0 ? Math.round((presentCount / totalCount) * 100) : 0}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Quick Actions</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => handleMarkAll(true)}
              disabled={!selectedCourseId || loading}
            >
              Mark All Present
            </Button>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => handleMarkAll(false)}
              disabled={!selectedCourseId || loading}
            >
              Mark All Absent
            </Button>
            <Button
              className="w-full"
              onClick={handleSaveAttendance}
              disabled={!selectedCourseId || saving || totalCount === 0}
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Attendance'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {selectedCourseId && (
        <Card>
          <CardHeader>
            <CardTitle>Student List</CardTitle>
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Search students..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading students...</div>
            ) : filteredStudents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {students.length === 0 ? 'No students enrolled in this course' : 'No students match your search'}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredStudents.map((student) => (
                  <div
                    key={student.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        checked={attendance[student.id] || false}
                        onCheckedChange={(checked) =>
                          handleAttendanceChange(student.id, checked as boolean)
                        }
                      />
                      <div>
                        <div className="font-medium">{student.name}</div>
                        {student.email && (
                          <div className="text-sm text-muted-foreground">{student.email}</div>
                        )}
                      </div>
                    </div>
                    <div className="text-sm">
                      {attendance[student.id] ? (
                        <span className="text-green-600 font-medium">Present</span>
                      ) : (
                        <span className="text-red-600 font-medium">Absent</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
