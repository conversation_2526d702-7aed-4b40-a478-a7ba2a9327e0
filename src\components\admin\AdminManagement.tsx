'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Phone, Mail, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  getAdminSettings,
  addAdminEmail,
  addAdminPhone,
  removeAdminEmail,
  removeAdminPhone,
} from '@/lib/auth';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import type { AdminSettings } from '@/types';

export function AdminManagement() {
  const [adminSettings, setAdminSettings] = useState<AdminSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [addType, setAddType] = useState<'email' | 'phone'>('email');
  const [newEmail, setNewEmail] = useState('');
  const [newPhone, setNewPhone] = useState('');
  const [saving, setSaving] = useState(false);
  
  const { user } = useAuth();

  useEffect(() => {
    loadAdminSettings();
  }, []);

  const loadAdminSettings = async () => {
    try {
      setLoading(true);
      const settings = await getAdminSettings();
      setAdminSettings(settings);
    } catch (error) {
      console.error('Error loading admin settings:', error);
      toast.error('Failed to load admin settings');
    } finally {
      setLoading(false);
    }
  };

  const handleAddAdmin = async () => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    if (addType === 'email' && !newEmail.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    if (addType === 'phone' && !newPhone.trim()) {
      toast.error('Please enter a phone number');
      return;
    }

    setSaving(true);
    
    try {
      if (addType === 'email') {
        await addAdminEmail(newEmail.trim(), user.uid);
        toast.success('Admin email added successfully');
        setNewEmail('');
      } else {
        await addAdminPhone(newPhone.trim(), user.uid);
        toast.success('Admin phone number added successfully');
        setNewPhone('');
      }
      
      setShowAddDialog(false);
      loadAdminSettings();
    } catch (error) {
      console.error('Error adding admin:', error);
      toast.error('Failed to add admin');
    } finally {
      setSaving(false);
    }
  };

  const handleRemoveEmail = async (email: string) => {
    if (!confirm(`Are you sure you want to remove admin access for ${email}?`)) {
      return;
    }

    try {
      await removeAdminEmail(email);
      toast.success('Admin email removed successfully');
      loadAdminSettings();
    } catch (error) {
      console.error('Error removing admin email:', error);
      toast.error('Failed to remove admin email');
    }
  };

  const handleRemovePhone = async (phone: string) => {
    if (!confirm(`Are you sure you want to remove admin access for ${phone}?`)) {
      return;
    }

    try {
      await removeAdminPhone(phone);
      toast.success('Admin phone number removed successfully');
      loadAdminSettings();
    } catch (error) {
      console.error('Error removing admin phone:', error);
      toast.error('Failed to remove admin phone number');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Admin Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading admin settings...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Admin Management</span>
            </div>
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Admin
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center space-x-2">
              <Mail className="h-4 w-4" />
              <span>Admin Email Addresses</span>
            </h4>
            {adminSettings?.adminEmails.length === 0 ? (
              <p className="text-sm text-muted-foreground">No admin email addresses configured</p>
            ) : (
              <div className="space-y-2">
                {adminSettings?.adminEmails.map((email) => (
                  <div key={email} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{email}</span>
                      {user?.email === email && (
                        <Badge variant="secondary" className="text-xs">You</Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveEmail(email)}
                      disabled={user?.email === email}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center space-x-2">
              <Phone className="h-4 w-4" />
              <span>Admin Phone Numbers</span>
            </h4>
            {adminSettings?.adminPhoneNumbers.length === 0 ? (
              <p className="text-sm text-muted-foreground">No admin phone numbers configured</p>
            ) : (
              <div className="space-y-2">
                {adminSettings?.adminPhoneNumbers.map((phone) => (
                  <div key={phone} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{phone}</span>
                      {user?.phoneNumber === phone && (
                        <Badge variant="secondary" className="text-xs">You</Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemovePhone(phone)}
                      disabled={user?.phoneNumber === phone}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Admin</DialogTitle>
            <DialogDescription>
              Add a new email address or phone number that can access the admin panel
            </DialogDescription>
          </DialogHeader>
          
          <Tabs value={addType} onValueChange={(value) => setAddType(value as 'email' | 'phone')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="email">Email</TabsTrigger>
              <TabsTrigger value="phone">Phone</TabsTrigger>
            </TabsList>
            
            <TabsContent value="email" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="newEmail">Email Address</Label>
                <Input
                  id="newEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="phone" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="newPhone">Phone Number</Label>
                <Input
                  id="newPhone"
                  type="tel"
                  placeholder="+1234567890"
                  value={newPhone}
                  onChange={(e) => setNewPhone(e.target.value)}
                />
              </div>
            </TabsContent>
          </Tabs>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddAdmin} disabled={saving}>
              {saving ? 'Adding...' : 'Add Admin'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
