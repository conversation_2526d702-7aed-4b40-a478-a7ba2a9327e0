'use client';

import React, { useState, useEffect } from 'react';
import { CreditCard, DollarSign, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { PageHeader } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Payment, Student, Course, PaymentStatus } from '@/types';
import { getStudents, getCourses } from '@/lib/firestore';
import { toast } from 'sonner';
import { format } from 'date-fns';

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<PaymentStatus | 'all'>('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [studentsData, coursesData] = await Promise.all([
        getStudents(),
        getCourses(),
      ]);
      setStudents(studentsData);
      setCourses(coursesData);
      
      // TODO: Load payments from Firestore
      setPayments([]);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const getStudentName = (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    return student?.name || 'Unknown Student';
  };

  const getCourseName = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    return course?.name || 'Unknown Course';
  };

  const getStatusBadge = (status: PaymentStatus) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'due':
        return <Badge variant="outline">Due</Badge>;
      case 'overdue':
        return <Badge variant="destructive">Overdue</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const filteredPayments = payments.filter(payment => 
    statusFilter === 'all' || payment.status === statusFilter
  );

  const columns: Column<Payment>[] = [
    {
      key: 'studentId',
      header: 'Student',
      render: (value) => getStudentName(value),
    },
    {
      key: 'courseId',
      header: 'Course',
      render: (value) => getCourseName(value),
    },
    {
      key: 'monthYear',
      header: 'Month',
      render: (value) => value,
    },
    {
      key: 'amount',
      header: 'Amount',
      render: (value, row) => (
        <div>
          <div className="font-medium">₹{value.toLocaleString()}</div>
          {row.discount > 0 && (
            <div className="text-sm text-muted-foreground">
              Discount: ₹{row.discount.toLocaleString()}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'finalAmount',
      header: 'Final Amount',
      render: (value) => `₹${value.toLocaleString()}`,
    },
    {
      key: 'status',
      header: 'Status',
      render: (value) => getStatusBadge(value),
    },
    {
      key: 'dueDate',
      header: 'Due Date',
      render: (value) => format(value.toDate(), 'MMM dd, yyyy'),
    },
  ];

  // Calculate summary statistics
  const totalDue = payments.filter(p => p.status === 'due').reduce((sum, p) => sum + p.finalAmount, 0);
  const totalOverdue = payments.filter(p => p.status === 'overdue').reduce((sum, p) => sum + p.finalAmount, 0);
  const totalPaid = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.finalAmount, 0);
  const totalRevenue = payments.reduce((sum, p) => sum + p.finalAmount, 0);

  return (
    <div className="space-y-6">
      <PageHeader
        title="Payments"
        description="Track and manage student payments across all courses"
        action={{
          label: 'Generate Bills',
          onClick: () => toast.info('Bill generation coming soon'),
        }}
      />

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              All time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid This Month</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">₹{totalPaid.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Collected payments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Dues</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">₹{totalDue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Current month dues
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">₹{totalOverdue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Past due payments
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center space-x-4">
        <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as PaymentStatus | 'all')}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Payments</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="due">Due</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <DataTable
        data={filteredPayments}
        columns={columns}
        loading={loading}
        searchable
        searchPlaceholder="Search payments..."
        emptyMessage="No payments found. Payment records will appear here once students are enrolled."
        actions={(payment) => (
          <div className="flex items-center space-x-2">
            {payment.status !== 'paid' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toast.info('Mark as paid coming soon')}
              >
                <CheckCircle className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toast.info('Payment details coming soon')}
            >
              <CreditCard className="h-4 w-4" />
            </Button>
          </div>
        )}
      />

      <Card>
        <CardHeader>
          <CardTitle>Payment Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2 md:grid-cols-3">
            <Button variant="outline" onClick={() => toast.info('Feature coming soon')}>
              <DollarSign className="h-4 w-4 mr-2" />
              Generate Monthly Bills
            </Button>
            <Button variant="outline" onClick={() => toast.info('Feature coming soon')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark Payments
            </Button>
            <Button variant="outline" onClick={() => toast.info('Feature coming soon')}>
              <AlertCircle className="h-4 w-4 mr-2" />
              Send Reminders
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Payment management features include automated billing, payment tracking, discount management, and reminder systems.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
