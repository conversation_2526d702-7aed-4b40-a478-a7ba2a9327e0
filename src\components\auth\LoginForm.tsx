"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ConfirmationResult, RecaptchaVerifier } from "firebase/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  initializeRecaptcha,
  sendOTP,
  verifyOTP,
  signInWithEmail,
  sendPasswordReset,
  isAdmin,
  initializeAdminSettings,
} from "@/lib/auth";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Phone, Mail, Eye, EyeOff } from "lucide-react";

export const LoginForm: React.FC = () => {
  // Phone authentication state
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState("");
  const [confirmationResult, setConfirmationResult] =
    useState<ConfirmationResult | null>(null);
  const [recaptchaVerifier, setRecaptchaVerifier] =
    useState<RecaptchaVerifier | null>(null);

  // Email authentication state
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [resetEmailSent, setResetEmailSent] = useState(false);

  // Common state
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("phone");

  const { isAdminUser } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAdminUser) {
      router.push("/admin");
    }
  }, [isAdminUser, router]);

  useEffect(() => {
    // Initialize admin settings and reCAPTCHA when component mounts
    const initializeAuth = async () => {
      try {
        await initializeAdminSettings();
      } catch (error) {
        console.error("Error initializing admin settings:", error);
      }
    };

    initializeAuth();

    const verifier = initializeRecaptcha("recaptcha-container");
    setRecaptchaVerifier(verifier);

    return () => {
      // Cleanup
      if (verifier) {
        verifier.clear();
      }
    };
  }, []);

  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!phoneNumber.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    if (!recaptchaVerifier) {
      toast.error("reCAPTCHA not initialized");
      return;
    }

    setLoading(true);

    try {
      const result = await sendOTP(phoneNumber, recaptchaVerifier);
      setConfirmationResult(result);
      toast.success("OTP sent successfully!");
    } catch (error: any) {
      console.error("Error sending OTP:", error);
      toast.error(error.message || "Failed to send OTP");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp.trim()) {
      toast.error("Please enter the OTP");
      return;
    }

    if (!confirmationResult) {
      toast.error("Please request OTP first");
      return;
    }

    setLoading(true);

    try {
      const user = await verifyOTP(confirmationResult, otp);

      const adminStatus = await isAdmin(user);
      if (adminStatus) {
        toast.success("Login successful!");
        router.push("/admin");
      } else {
        toast.error("Access denied. Only admin can login.");
        // You might want to sign out the user here
      }
    } catch (error: any) {
      console.error("Error verifying OTP:", error);
      toast.error(error.message || "Invalid OTP");
    } finally {
      setLoading(false);
    }
  };

  // Email authentication handlers
  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !password.trim()) {
      toast.error("Please enter both email and password");
      return;
    }

    setLoading(true);

    try {
      const user = await signInWithEmail(email, password);

      const adminStatus = await isAdmin(user);
      if (adminStatus) {
        toast.success("Login successful!");
        router.push("/admin");
      } else {
        toast.error("Access denied. Only admin can login.");
      }
    } catch (error: any) {
      console.error("Error signing in with email:", error);
      if (error.code === "auth/user-not-found") {
        toast.error("No account found with this email address");
      } else if (error.code === "auth/wrong-password") {
        toast.error("Incorrect password");
      } else if (error.code === "auth/invalid-email") {
        toast.error("Invalid email address");
      } else {
        toast.error(error.message || "Failed to sign in");
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!email.trim()) {
      toast.error("Please enter your email address first");
      return;
    }

    setLoading(true);

    try {
      await sendPasswordReset(email);
      setResetEmailSent(true);
      toast.success("Password reset email sent! Check your inbox.");
    } catch (error: any) {
      console.error("Error sending password reset:", error);
      if (error.code === "auth/user-not-found") {
        toast.error("No account found with this email address");
      } else {
        toast.error(error.message || "Failed to send password reset email");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            Admin Login
          </CardTitle>
          <CardDescription className="text-center">
            Sign in with your phone number or email address
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger
                value="phone"
                className="flex items-center space-x-2"
              >
                <Phone className="h-4 w-4" />
                <span>Phone</span>
              </TabsTrigger>
              <TabsTrigger
                value="email"
                className="flex items-center space-x-2"
              >
                <Mail className="h-4 w-4" />
                <span>Email</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="phone" className="space-y-4 mt-4">
              {!confirmationResult ? (
                <form onSubmit={handleSendOTP} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="+1234567890"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "Sending..." : "Send OTP"}
                  </Button>
                </form>
              ) : (
                <form onSubmit={handleVerifyOTP} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="otp">Enter OTP</Label>
                    <Input
                      id="otp"
                      type="text"
                      placeholder="123456"
                      value={otp}
                      onChange={(e) => setOtp(e.target.value)}
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "Verifying..." : "Verify OTP"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      setConfirmationResult(null);
                      setOtp("");
                    }}
                  >
                    Back to Phone Number
                  </Button>
                </form>
              )}
            </TabsContent>

            <TabsContent value="email" className="space-y-4 mt-4">
              <form onSubmit={handleEmailLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Signing in..." : "Sign In"}
                </Button>

                <div className="text-center">
                  <Button
                    type="button"
                    variant="link"
                    className="text-sm"
                    onClick={handlePasswordReset}
                    disabled={loading || !email.trim()}
                  >
                    {resetEmailSent ? "Email sent!" : "Forgot password?"}
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>

          {/* reCAPTCHA container */}
          <div id="recaptcha-container"></div>
        </CardContent>
      </Card>
    </div>
  );
};
