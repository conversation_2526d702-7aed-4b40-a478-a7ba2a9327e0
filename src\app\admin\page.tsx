'use client';

import React from 'react';
import { PageHeader } from '@/components/ui/page-header';
import { StatsCard } from '@/components/ui/stats-card';
import { 
  Users, 
  BookOpen, 
  Calendar, 
  CreditCard, 
  TrendingUp, 
  FileText 
} from 'lucide-react';

export default function AdminDashboard() {
  // TODO: Replace with real data from Firebase
  const stats = {
    totalCourses: 5,
    totalStudents: 120,
    totalRevenue: 45000,
    pendingPayments: 8500,
    averageAttendance: 85,
    activeExams: 3,
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        description="Overview of your education management system"
      />
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <StatsCard
          title="Total Courses"
          value={stats.totalCourses}
          description="Active courses"
          icon={BookOpen}
        />
        
        <StatsCard
          title="Total Students"
          value={stats.totalStudents}
          description="Enrolled students"
          icon={Users}
        />
        
        <StatsCard
          title="Total Revenue"
          value={`₹${stats.totalRevenue.toLocaleString()}`}
          description="This month"
          icon={TrendingUp}
        />
        
        <StatsCard
          title="Pending Payments"
          value={`₹${stats.pendingPayments.toLocaleString()}`}
          description="Outstanding dues"
          icon={CreditCard}
        />
        
        <StatsCard
          title="Average Attendance"
          value={`${stats.averageAttendance}%`}
          description="This month"
          icon={Calendar}
        />
        
        <StatsCard
          title="Active Exams"
          value={stats.activeExams}
          description="Ongoing assessments"
          icon={FileText}
        />
      </div>
      
      {/* TODO: Add recent activities, charts, and quick actions */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Activities</h3>
          <p className="text-muted-foreground">Recent activities will be displayed here.</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <p className="text-muted-foreground">Quick action buttons will be displayed here.</p>
        </div>
      </div>
    </div>
  );
}
